package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"math/rand"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

const (
	// TODO: make this configurable
	MaxActiveUserSize = 1000

	// Redis bet retention periods
	RegularBetRetentionPeriod    = 10 * time.Minute
	HighPayoutBetRetentionPeriod = 1 * time.Hour

	// Queue rotation period
	ActiveUserQueueRotationPeriod = 2 * time.Minute
)

// Assert interface implementation
var _ domain.BetSimulatorService = (*BetSimulatorService)(nil)

type BetSimulatorService struct {
	config         domain.SimulationConfig
	status         domain.SimulationStatus
	sessionState   domain.SessionState
	simulationData domain.BetSimulationData
	hubBets        chan<- domain.Bet
	betRepository  domain.BetRepository
	gameRepository domain.GameRepository
	ticker         *time.Ticker
	stopChan       chan struct{}
	mutex          sync.RWMutex
	isRunning      bool
	rand           *rand.Rand
	recentUsers    []uuid.UUID // last 2 user IDs to enforce diversity
	redisClient    *redis.Client
	sentBetIDs     map[uuid.UUID]time.Time // Track recently sent bet IDs to prevent websocket duplicates
	// Active user queue for realism
	activeUserQueue   []*domain.SimulatedUserData
	activeUserGames   map[uuid.UUID]*domain.SimulatedGameData // Maps user ID to their current game
	lastQueueRotation time.Time
}

func NewBetSimulatorService(
	hubBets chan<- domain.Bet,
	betRepository domain.BetRepository,
	gameRepository domain.GameRepository,
) *BetSimulatorService {
	service := &BetSimulatorService{
		hubBets:           hubBets,
		betRepository:     betRepository,
		gameRepository:    gameRepository,
		stopChan:          make(chan struct{}),
		rand:              rand.New(rand.NewSource(time.Now().UnixNano())),
		recentUsers:       make([]uuid.UUID, 0, 2),
		sentBetIDs:        make(map[uuid.UUID]time.Time),
		activeUserQueue:   make([]*domain.SimulatedUserData, 0, MaxActiveUserSize),
		activeUserGames:   make(map[uuid.UUID]*domain.SimulatedGameData),
		lastQueueRotation: time.Now(),
	}

	return service
}

func (s *BetSimulatorService) StartSimulation(ctx context.Context, config domain.SimulationConfig) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		slog.Info("Slated bet simulation already running")
		return fmt.Errorf("simulation is already running")
	}

	if !config.Enabled {
		slog.Info("Slated bet simulation is disabled in config")
		return fmt.Errorf("simulation is disabled in config")
	}

	s.config = config

	// initialize data based on provided lists
	s.initializeSimulationData()

	// Initialize active user queue
	s.initializeActiveUserQueue()

	s.status = domain.SimulationStatus{
		IsActive: true,
	}
	s.isRunning = true

	// Start the simulation goroutine
	go s.runSimulation(ctx)

	slog.Info("Slated bet simulation started",
		"intervalRange", config.NewBetIntervalRange,
		"highPayoutProbability", config.HighPayoutProbability,
		"highPayoutThreshold", config.HighPayoutThreshold,
		"userCount", len(config.UsernameList),
		"gameCount", len(config.Games))

	return nil
}

func (s *BetSimulatorService) StopSimulation() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		slog.Warn("Slated bet simulation is not running")
		return fmt.Errorf("simulation is not running")
	}

	close(s.stopChan)
	if s.ticker != nil {
		s.ticker.Stop()
	}

	s.isRunning = false
	s.status.IsActive = false

	slog.Info("Slated bet simulation stopped",
		"totalBetsGenerated", s.status.TotalSimulatedBets,
		"highPayoutBetsGenerated", s.status.HighPayoutBetsGenerated)
	return nil
}

func (s *BetSimulatorService) GetSimulationStatus() domain.SimulationStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.status
}

func (s *BetSimulatorService) GenerateSimulatedBet(ctx context.Context, timestamp time.Time, forceHighPayout bool) (*domain.Bet, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.sessionState.CurrentUser == nil || s.sessionState.SessionBetsLeft <= 0 {
		s.pickNewSession()
	}

	s.sessionState.SessionBetsLeft--

	usr := s.sessionState.CurrentUser

	betAmount := s.flattenBetAmount(s.sessionState.BaseBetAmount)
	multiplier := s.getRandomMultiplier(s.sessionState.CurrentGame.Name)
	payout := utils.RoundFloat(betAmount*multiplier, 3)

	usr.TotalBets++
	usr.Wagered += betAmount
	usr.TotalCoins += payout
	if payout > 0 {
		usr.TotalWins++
	} else {
		usr.TotalLoses++
	}
	if usr.TotalBets > 0 {
		usr.WinRate = math.Round(float64(usr.TotalWins)/float64(usr.TotalBets)*100*100) / 100 // two-decimals
	}

	if forceHighPayout || s.rand.Float64() < s.config.HighPayoutProbability {
		// pick a clean integer multiple of 10 between 200 and 600
		betAmount = float64((20 + s.rand.Intn(41)) * 10) // 200,210,...,600
		multipliers := []float64{1.5, 2.0, 2.25, 2.5, 3.0}
		multiplier = multipliers[s.rand.Intn(len(multipliers))]
		payout = utils.RoundFloat(betAmount*multiplier, 3)
		s.status.HighPayoutBetsGenerated++

		slog.Info("Generated high payout slated bet",
			"betAmount", betAmount,
			"multiplier", multiplier,
			"payout", payout,
			"user", usr.UserName,
			"game", s.sessionState.CurrentGame.Name,
			"forceHighPayout", forceHighPayout)
	}

	// Ensure bet amount always has exactly 2 decimal places for Redis storage
	betAmount = math.Round(betAmount*100) / 100

	bet := &domain.Bet{
		ID:              uuid.New(),
		ExternalID:      uuid.NewString(),
		BetAmount:       &betAmount,
		ActualBetAmount: &betAmount,
		BetType:         domain.BetTypeCasino,
		Currency:        "USD",
		ConvertedTo:     "USD",
		Multiplier:      multiplier,
		Payout:          &payout,
		ActualWinAmount: &payout,
		RoundStatus:     "completed",
		Time:            timestamp,
		GhostMode:       s.sessionState.CurrentUser.GhostMode,
		HiddenBoolean:   s.sessionState.CurrentUser.HideAllStats,
		CoinsMultiplier: 1.0,
		Game: domain.Game{
			ID:          s.sessionState.CurrentGame.ID,
			ExternalID:  s.sessionState.CurrentGame.ExternalID,
			Name:        &s.sessionState.CurrentGame.Name,
			CMSGameID:   s.sessionState.CurrentGame.CMSGameID,
			Slug:        &s.sessionState.CurrentGame.Slug,
			ThumbnailID: &s.sessionState.CurrentGame.ThumbnailID,
		},
		User: domain.User{
			ID:             s.sessionState.CurrentUser.ID,
			UserName:       usr.UserName,
			VIPStatus:      s.sessionState.CurrentUser.VIPStatus,
			GhostMode:      s.sessionState.CurrentUser.GhostMode,
			HideAllStats:   s.sessionState.CurrentUser.HideAllStats,
			NumberOfLosses: usr.TotalLoses,
			NumberOfWins:   usr.TotalWins,
			TotalBets:      usr.TotalBets,
			Wagered:        usr.Wagered,
			XP:             0,
			TotalCoins:     usr.TotalCoins,
		},
	}

	s.status.TotalSimulatedBets++
	s.status.LastBetGenerated = timestamp

	s.cleanupRedisWithUserPreservation()

	slog.Debug("Generated slated bet",
		"betId", bet.ID,
		"externalId", bet.ExternalID,
		"user", bet.User.UserName,
		"game", *bet.Game.Name,
		"betAmount", *bet.BetAmount,
		"multiplier", bet.Multiplier,
		"payout", *bet.Payout,
		"sessionBetsLeft", s.sessionState.SessionBetsLeft,
		"isAutoplay", s.sessionState.IsAutoplay)

	return bet, nil
}

// SetRedisClient injects redis client to store slated bets.
func (s *BetSimulatorService) SetRedisClient(c *redis.Client) {
	s.redisClient = c
	if c != nil {
		slog.Info("Redis client set for slated bet storage")
	} else {
		slog.Warn("Redis client cleared for slated bet storage")
	}
}

func (s *BetSimulatorService) runSimulation(ctx context.Context) {
	slog.Info("Slated bet simulation loop started")

	// Create initial ticker
	interval := s.calculateNextInterval()
	s.ticker = time.NewTicker(interval)
	defer s.ticker.Stop()

	for {
		select {
		case <-s.ticker.C:
			now := time.Now()

			// Rotate active user queue every 10 minutes
			s.mutex.Lock()
			s.rotateActiveUserQueue()

			// Ensure we have an active session and next user differs from last one
			if s.sessionState.SessionBetsLeft == 0 || (len(s.recentUsers) >= 1 && s.sessionState.CurrentUser.ID == s.recentUsers[0]) {
				// pick until we get a different user (max 5 attempts to avoid infinite loop)
				attempts := 0
				for attempts < 5 {
					s.pickNewSession()
					if len(s.recentUsers) == 0 || s.sessionState.CurrentUser.ID != s.recentUsers[0] {
						break
					}
					attempts++
				}
				if attempts >= 5 {
					slog.Warn("Failed to find different user after 5 attempts for slated bet generation")
				}
			}
			s.mutex.Unlock()

			bet, err := s.GenerateSimulatedBet(ctx, now, false)
			if err != nil {
				slog.Error("Failed to generate slated bet", "error", err, "timestamp", now)
				// Reset ticker for next attempt
				s.ticker.Stop()
				interval := s.calculateNextInterval()
				s.ticker = time.NewTicker(interval)
				continue
			}

			// update recent users list
			s.mutex.Lock()
			s.recentUsers = append([]uuid.UUID{bet.User.ID}, s.recentUsers...)
			if len(s.recentUsers) > 2 {
				s.recentUsers = s.recentUsers[:2]
			}
			s.mutex.Unlock()

			// Check if we already sent this bet ID to prevent duplicates
			s.mutex.Lock()
			if sentTime, exists := s.sentBetIDs[bet.ID]; exists {
				s.mutex.Unlock()
				slog.Warn("Preventing duplicate bet send to websocket",
					"betId", bet.ID,
					"user", bet.User.UserName,
					"game", *bet.Game.Name,
					"originalSentTime", sentTime)
				// Reset ticker for next bet
				s.ticker.Stop()
				interval := s.calculateNextInterval()
				s.ticker = time.NewTicker(interval)
				continue
			}
			s.sentBetIDs[bet.ID] = now
			s.mutex.Unlock()

			// Store in Redis and publish only on success
			if s.pushRedis(bet) {
				select {
				case s.hubBets <- *bet:
					slog.Info("Slated bet sent to hub",
						"betId", bet.ID,
						"externalId", bet.ExternalID,
						"user", bet.User.UserName,
						"game", *bet.Game.Name,
						"amount", *bet.BetAmount,
						"payout", *bet.Payout)

				default:
					slog.Warn("Hub channel full, dropping slated bet",
						"betId", bet.ID,
						"user", bet.User.UserName,
						"game", *bet.Game.Name)
				}
			}

			// Reset ticker for next bet
			s.ticker.Stop()
			interval := s.calculateNextInterval()
			s.ticker = time.NewTicker(interval)

		case <-ctx.Done():
			slog.Info("Slated bet simulation loop stopped due to context cancellation")
			return
		case <-s.stopChan:
			slog.Info("Slated bet simulation loop stopped due to stop signal")
			return
		}
	}
}

func (s *BetSimulatorService) calculateNextInterval() time.Duration {
	randomMs := s.config.NewBetIntervalRange[0] +
		s.rand.Intn(s.config.NewBetIntervalRange[1]-s.config.NewBetIntervalRange[0])
	s.status.CurrentInterval = time.Duration(randomMs) * time.Millisecond
	return s.status.CurrentInterval
}

func (s *BetSimulatorService) pickNewSession() {
	// Select user from active queue instead of full user pool
	var user *domain.SimulatedUserData
	if len(s.activeUserQueue) > 0 {
		userIdx := s.rand.Intn(len(s.activeUserQueue))
		user = s.activeUserQueue[userIdx]
	} else {
		// Reinitialize queue if it's empty
		slog.Warn("Active user queue is empty, reinitializing")
		s.initializeActiveUserQueue()
		if len(s.activeUserQueue) > 0 {
			userIdx := s.rand.Intn(len(s.activeUserQueue))
			user = s.activeUserQueue[userIdx]
		} else {
			// Ultimate fallback if we still can't fill the queue
			userIdx := s.rand.Intn(len(s.simulationData.Users))
			user = &s.simulationData.Users[userIdx]
		}
	}

	// Use the assigned game for this user, or fallback to random if not found
	var game *domain.SimulatedGameData
	if assignedGame, exists := s.activeUserGames[user.ID]; exists {
		game = assignedGame
	} else {
		// Fallback to random game (shouldn't happen for active users)
		gameIdx := s.rand.Intn(len(s.simulationData.Games))
		game = &s.simulationData.Games[gameIdx]
		slog.Warn("No assigned game found for user, using random", "user", user.UserName)
	}

	isAutoplay := s.rand.Float64() < 0.35 // 35% chance of autoplay

	var sessionBetsLeft int
	if isAutoplay {
		sessionBetsLeft = 8 + s.rand.Intn(9) // 8-16 rounds
	} else {
		sessionBetsLeft = 2 + s.rand.Intn(3) // 2-4 rounds
	}

	baseBetAmount := s.getBaseBetAmount(game.Name)
	if !isAutoplay {
		variance := (s.rand.Float64()*0.2 - 0.1) * baseBetAmount // ±10% variance
		baseBetAmount += variance
	}

	s.sessionState = domain.SessionState{
		CurrentUser:      user,
		CurrentGame:      game,
		SessionBetsLeft:  sessionBetsLeft,
		BaseBetAmount:    math.Round(baseBetAmount*100) / 100,
		IsAutoplay:       isAutoplay,
		SessionStartTime: time.Now(),
	}

	slog.Debug("New slated bet session started",
		"user", user.UserName,
		"game", game.Name,
		"isAutoplay", isAutoplay,
		"betsLeft", sessionBetsLeft,
		"baseBetAmount", baseBetAmount,
		"vipStatus", user.VIPStatus)
}

// TODO: implement game specific base bet amounts
func (s *BetSimulatorService) getBaseBetAmount(gameName string) float64 {
	r := s.rand.Float64()

	var min, max float64
	switch {
	case r < 0.8:
		// 80%: 1 – 20
		min, max = 1.0, 20.0
	case r < 0.95:
		// next 15%: 20 – 100
		min, max = 20.0, 100.0
	default:
		// remaining 5%: 100 – 1000
		min, max = 100.0, 1000.0
	}

	var amount float64
	// For the main range [1,20], use uniform distribution instead of log-uniform
	// to get more even distribution across the range
	if r < 0.8 {
		amount = min + s.rand.Float64()*(max-min)
	} else {
		// log-uniform sampling to favour lower values inside higher brackets
		amount = math.Pow(10, math.Log10(min)+s.rand.Float64()*(math.Log10(max)-math.Log10(min)))
	}

	// For amounts in [1,20] range, occasionally snap to nice round numbers
	if amount >= 1.0 && amount <= 20.0 && s.rand.Float64() < 0.3 {
		// 30% chance to snap to nearest integer for nicer amounts in the main range
		amount = math.Round(amount)
	}

	// always return value rounded to 2 decimal places (e.g., 20 -> 20.00)
	return math.Round(amount*100) / 100
}

// getRandomMultiplier returns a game-specific multiplier based on the game's configuration
func (s *BetSimulatorService) getRandomMultiplier(gameName string) float64 {
	// Find the current game to get its multiplier configuration
	var currentGame *domain.SimulatedGameData
	if s.sessionState.CurrentGame != nil {
		currentGame = s.sessionState.CurrentGame
	}

	// If no game configuration or no multiplier data, use default behavior
	if currentGame == nil || len(currentGame.WinMultipliers) == 0 || len(currentGame.WinWeights) == 0 {
		return s.getDefaultMultiplier()
	}

	// Use game-specific loss rate (default to 0.6 if not set)
	lossRate := currentGame.LossRate
	if lossRate == 0 {
		lossRate = 0.6 // Default 60% loss rate
	}

	// Check for losing bet
	if s.rand.Float64() < lossRate {
		return 0
	}

	// For winning bets, use weighted selection from game's win multipliers
	return s.selectWeightedMultiplier(currentGame.WinMultipliers, currentGame.WinWeights)
}

// getDefaultMultiplier provides the original multiplier logic as fallback
func (s *BetSimulatorService) getDefaultMultiplier() float64 {
	// 70% chance of multiplier 0
	if s.rand.Float64() < 0.7 {
		return 0
	}

	// For winning bets: use weighted intervals
	intervals := [][]float64{{0.5, 1.0}, {1.0, 2.0}, {2.0, 5.0}, {5.0, 9.0}}
	probabilities := []float64{0.1, 0.6, 0.2, 0.1}

	// Select interval based on probabilities
	r := s.rand.Float64()
	cumulative := 0.0
	selectedInterval := intervals[0] // fallback

	for i, prob := range probabilities {
		cumulative += prob
		if r <= cumulative {
			selectedInterval = intervals[i]
			break
		}
	}

	// Generate multiplier within selected interval
	min, max := selectedInterval[0], selectedInterval[1]
	mult := min + s.rand.Float64()*(max-min)

	// Round to ensure second decimal place is zero (e.g., 1.20, 5.70)
	return math.Floor(mult*10) / 10
}

// selectWeightedMultiplier selects a multiplier from the list using weighted probabilities
func (s *BetSimulatorService) selectWeightedMultiplier(multipliers []float64, weights []float64) float64 {
	if len(multipliers) != len(weights) || len(multipliers) == 0 {
		// Fallback to default if configuration is invalid
		return s.getDefaultMultiplier()
	}

	// Calculate total weight
	totalWeight := 0.0
	for _, weight := range weights {
		totalWeight += weight
	}

	if totalWeight == 0 {
		// Fallback if all weights are zero
		return s.getDefaultMultiplier()
	}

	// Generate random number between 0 and totalWeight
	r := s.rand.Float64() * totalWeight

	// Find the selected multiplier
	cumulative := 0.0
	for i, weight := range weights {
		cumulative += weight
		if r <= cumulative {
			return utils.RoundFloat(multipliers[i], 2)
		}
	}

	// Fallback to last multiplier (shouldn't happen)
	return utils.RoundFloat(multipliers[len(multipliers)-1], 2)
}

func (s *BetSimulatorService) initializeSimulationData() {
	games := s.config.Games
	usernameList := s.config.UsernameList

	slog.Info("Initializing slated bet simulation data",
		"gameCount", len(games),
		"userCount", len(usernameList))

	// Build vip tier names and XP ranges from configuration (already loaded from CSV)
	type xpRange struct{ min, max float64 }
	var xpRanges []xpRange
	var vipTiers []string

	if len(s.config.VipTiers) > 0 {
		for _, cfg := range s.config.VipTiers {
			vipTiers = append(vipTiers, cfg.Name)
			xpRanges = append(xpRanges, xpRange{min: cfg.XPRange[0], max: cfg.XPRange[1]})
		}
		slog.Debug("Using VIP tiers from config for slated bet simulation", "tierCount", len(vipTiers))
	} else {
		slog.Warn("No VIP tiers configured for slated bet simulation, using defaults")
		// Fallback to default VIP tiers if none configured
		vipTiers = []string{"Baboon", "Bronze", "Silver", "Gold", "Platinum", "Diamond"}
		xpRanges = []xpRange{
			{min: 0, max: 9999},
			{min: 10000, max: 49999},
			{min: 50000, max: 99999},
			{min: 100000, max: 249999},
			{min: 250000, max: 499999},
			{min: 500000, max: 1000000},
		}
	}

	users := make([]domain.SimulatedUserData, 0, len(usernameList))
	for _, uname := range usernameList {
		joinDaysAgo := s.rand.Intn(90) + 1 // 1-90 days ago

		// Pick a VIP tier index with higher probability for lower tiers (linear decrease)
		tierIdx := s.linearWeightedIndex(len(vipTiers))

		vip := vipTiers[tierIdx]

		// Pick an XP value within the tier range
		rangeCfg := xpRanges[tierIdx]
		xp := rangeCfg.min + s.rand.Float64()*(rangeCfg.max-rangeCfg.min)

		// Split XP origin by betting vertical
		casinoShare := 0.7 + s.rand.Float64()*0.2 // 70-90 % from casino, rest from sports
		casinoXP := xp * casinoShare
		sportsXP := xp - casinoXP

		// Apply conversion rules to estimate total USD wagered
		casinoWagered := casinoXP     // 1 USD == 1 XP
		sportsWagered := sportsXP / 3 // 1 USD == 3 XP
		totalWagered := casinoWagered + sportsWagered

		// Derive number of bets from wagered using an average bet size that grows with tier
		avgBet := 1.0 + float64(tierIdx)*2.0 // low tiers bet smaller amounts
		if avgBet < 1 {
			avgBet = 1
		}
		totalBets := int(totalWagered / avgBet)
		if totalBets < 10 {
			totalBets = 10 // keep a sane minimum
		}

		// Win-rate bias: higher tiers slightly better (10-50 %)
		baseWinRate := 0.10 + 0.03*float64(tierIdx) // 10 % + 3 % per tier step
		if baseWinRate > 0.5 {
			baseWinRate = 0.5
		}
		winRate := baseWinRate + s.rand.Float64()*0.1 // add ±10 % noise
		if winRate > 0.9 {
			winRate = 0.9
		}

		totalWins := int(float64(totalBets) * winRate)
		totalLoses := totalBets - totalWins
		coins := float64(totalWins) * 10.0

		user := domain.SimulatedUserData{
			ID:           uuid.New(),
			UserName:     uname,
			VIPStatus:    vip,
			JoinDate:     time.Now().AddDate(0, 0, -joinDaysAgo),
			GhostMode:    false,
			HideAllStats: false,
			TotalBets:    totalBets,
			TotalWins:    totalWins,
			TotalLoses:   totalLoses,
			Wagered:      math.Round(totalWagered*100) / 100,
			WinRate:      math.Round(winRate*10000) / 100, // percentage with 2 decimals
			XP:           math.Round(xp*100) / 100,
			TotalCoins:   math.Round(coins*100) / 100,
		}

		users = append(users, user)
	}

	s.simulationData = domain.BetSimulationData{
		Games: games,
		Users: users,
	}

	slog.Info("Slated bet simulation data initialized successfully",
		"totalUsers", len(users),
		"totalGames", len(games))
}

// flattenBetAmount rounds bet amounts to more "flat" values.
// If amount ≥ 1 it is rounded to the nearest integer.
// For sub-unitary amounts (<1) it keeps two decimals (e.g., 0.20).
func (s *BetSimulatorService) flattenBetAmount(amount float64) float64 {
	if amount >= 1.0 {
		// 90% chance to round to whole number
		if s.rand.Float64() < 0.9 {
			return math.Round(amount)
		}
		return math.Round(amount*100) / 100.0
	}
	return math.Round(amount*100) / 100.0
}

// pushRedis stores the bet in a sorted-set keyed by environment with timestamp score and trims older than 10m.
// Exception: If a user has no bets in the last 10 minutes, their most recent bet is preserved
// even if it's older than 10 minutes.
func (s *BetSimulatorService) pushRedis(bet *domain.Bet) bool {
	if s.redisClient == nil {
		slog.Warn("Redis client is nil – cannot store slated bet")
		return false
	}
	data, err := json.Marshal(bet)
	if err != nil {
		slog.Error("Failed to marshal slated bet for redis", slog.Any("error", err))
		return false
	}
	if bet.Time.IsZero() {
		slog.Warn("Bet time is zero – skipping Redis push", "bet", bet)
		return false
	}
	score := float64(bet.Time.UnixMilli())
	ctx := context.Background()
	key := utils.GetSlatedBetsKey()
	if err := s.redisClient.ZAdd(ctx, key, redis.Z{Score: score, Member: data}).Err(); err != nil {
		slog.Error("Failed to ZADD slated bet", slog.Any("error", err))
		return false
	}

	// Apply intelligent cleanup with user preservation logic
	s.cleanupRedisWithUserPreservation()
	return true
}

// cleanupRedisWithUserPreservation removes old bets from Redis with the same user preservation logic
// as the in-memory cleanup: preserves the most recent bet for users with no recent activity.
func (s *BetSimulatorService) cleanupRedisWithUserPreservation() {
	if s.redisClient == nil {
		return
	}

	ctx := context.Background()
	key := utils.GetSlatedBetsKey()
	now := time.Now()
	regularCutoff := now.Add(-RegularBetRetentionPeriod)       // Regular bets
	highPayoutCutoff := now.Add(-HighPayoutBetRetentionPeriod) // High payout bets

	// Get all bets from Redis
	vals, err := s.redisClient.ZRangeByScore(ctx, key, &redis.ZRangeBy{Min: "-inf", Max: "+inf"}).Result()
	if err != nil {
		slog.Error("Failed to fetch bets from Redis for cleanup", "error", err)
		return
	}

	if len(vals) == 0 {
		return
	}

	// Parse all bets and group by user ID (UUID)
	userBets := make(map[uuid.UUID][]domain.Bet)
	userMostRecent := make(map[uuid.UUID]domain.Bet)
	allBets := make([]domain.Bet, 0, len(vals))

	for _, val := range vals {
		var redisBet domain.Bet
		if json.Unmarshal([]byte(val), &redisBet) != nil {
			continue
		}

		allBets = append(allBets, redisBet)
		userID := redisBet.User.ID
		userBets[userID] = append(userBets[userID], redisBet)

		// Track most recent bet for this user ID
		if mostRecent, exists := userMostRecent[userID]; !exists || redisBet.Time.After(mostRecent.Time) {
			userMostRecent[userID] = redisBet
		}
	}

	// Determine which bets to remove
	betsToRemove := make([]string, 0)

	for userID, userBetList := range userBets {
		mostRecentBet := userMostRecent[userID]

		// Check if user has any bets within the last 10 minutes
		hasRecentBets := false
		for _, userBet := range userBetList {
			if userBet.Time.After(regularCutoff) {
				hasRecentBets = true
				break
			}
		}

		// Mark old bets for removal, but preserve most recent if user has no recent activity
		for _, userBet := range userBetList {
			// Determine the appropriate cutoff time based on whether it's a high payout bet
			cutoffTime := regularCutoff
			if s.isHighPayoutBet(userBet) {
				cutoffTime = highPayoutCutoff
			}

			if userBet.Time.Before(cutoffTime) {
				// Remove old bet unless it's the most recent and user has no recent bets
				if hasRecentBets || userBet.ID != mostRecentBet.ID {
					if betData, err := json.Marshal(userBet); err == nil {
						betsToRemove = append(betsToRemove, string(betData))
					}
				} else {
					// slog.Debug("Preserving old Redis bet for user with no recent activity",
					// 	"userId", userID,
					// 	"userName", userBet.User.UserName,
					// 	"betTime", userBet.Time,
					// 	"betAge", now.Sub(userBet.Time))
				}
			}
		}
	}

	// Remove the identified bets from Redis
	if len(betsToRemove) > 0 {
		pipe := s.redisClient.Pipeline()
		for _, betData := range betsToRemove {
			pipe.ZRem(ctx, key, betData)
		}
		if _, err := pipe.Exec(ctx); err != nil {
			slog.Error("Failed to remove old bets from Redis", "error", err)
		} else {
			slog.Debug("Cleaned up Redis bets with user preservation logic",
				"removedCount", len(betsToRemove),
				"totalBets", len(allBets))
		}
	}
}

// isHighPayoutBet determines if a bet should be preserved for 1 hour instead of 10 minutes
func (s *BetSimulatorService) isHighPayoutBet(bet domain.Bet) bool {
	if bet.Payout == nil || bet.BetAmount == nil {
		return false
	}

	payout := *bet.Payout

	return s.config.HighPayoutThreshold > 0 && payout >= s.config.HighPayoutThreshold
}

// linearWeightedIndex returns a pseudo-random index in [0,n) where the probability
// of picking index i is proportional to (n - i). This gives higher weight to
// smaller indices and linearly decreases the weight as the index grows.
// It panics if n <= 0.
func (s *BetSimulatorService) linearWeightedIndex(n int) int {
	if n <= 0 {
		panic("linearWeightedIndex called with non-positive n")
	}

	totalWeight := n * (n + 1) / 2    // 1+2+…+n
	r := s.rand.Intn(totalWeight) + 1 // 1..totalWeight inclusive

	cumulative := 0
	for i := 0; i < n; i++ {
		weight := n - i
		cumulative += weight
		if r <= cumulative {
			return i
		}
	}
	return n - 1 // fallback; should never happen
}

// initializeActiveUserQueue fills the active user queue with random users and assigns them games
func (s *BetSimulatorService) initializeActiveUserQueue() {
	size := s.config.ActiveUserQueueSize
	if size <= 0 {
		size = MaxActiveUserSize
	}

	s.activeUserQueue = make([]*domain.SimulatedUserData, 0, size)
	s.activeUserGames = make(map[uuid.UUID]*domain.SimulatedGameData)
	s.lastQueueRotation = time.Now()

	// Select random users from the full pool until the queue reaches the desired size.
	for len(s.activeUserQueue) < size && len(s.activeUserQueue) < len(s.simulationData.Users) {
		userIdx := s.rand.Intn(len(s.simulationData.Users))
		user := &s.simulationData.Users[userIdx]

		// Check if user is already in the queue
		alreadyInQueue := false
		for _, queueUser := range s.activeUserQueue {
			if queueUser.ID == user.ID {
				alreadyInQueue = true
				break
			}
		}

		if !alreadyInQueue {
			s.activeUserQueue = append(s.activeUserQueue, s.copyAndSetUserDetails(*user))

			// Assign a random game to this user for the period they are in the active user queue
			gameIdx := s.rand.Intn(len(s.simulationData.Games))
			game := &s.simulationData.Games[gameIdx]
			s.activeUserGames[user.ID] = game
		}
	}

	slog.Info("Active user queue initialized", "queueSize", len(s.activeUserQueue), "gamesAssigned", len(s.activeUserGames))
}

// rotateActiveUserQueue removes the oldest user and adds a new one based on the rotation period
// Also reassigns games to all active users for the new period
func (s *BetSimulatorService) rotateActiveUserQueue() {
	if time.Since(s.lastQueueRotation) < ActiveUserQueueRotationPeriod {
		return
	}

	if len(s.activeUserQueue) == 0 {
		return
	}

	// Store the user being removed for logging
	removedUser := s.activeUserQueue[0]

	// Remove the first user (oldest) and their game assignment
	s.activeUserQueue = s.activeUserQueue[1:]
	delete(s.activeUserGames, removedUser.ID)

	// Create a pool of users excluding the active ones
	var availableUsers []*domain.SimulatedUserData
	for i := range s.simulationData.Users {
		user := &s.simulationData.Users[i]

		// Check if user is already in the queue
		alreadyInQueue := false
		for _, queueUser := range s.activeUserQueue {
			if queueUser.ID == user.ID {
				alreadyInQueue = true
				break
			}
		}

		if !alreadyInQueue {
			availableUsers = append(availableUsers, user)
		}
	}

	// Add a new user from the available pool
	var newUser *domain.SimulatedUserData
	if len(availableUsers) > 0 {
		userIdx := s.rand.Intn(len(availableUsers))
		newUser = s.copyAndSetUserDetails(*availableUsers[userIdx])
		s.activeUserQueue = append(s.activeUserQueue, newUser)
	} else {
		// Fallback: if no available users
		userIdx := s.rand.Intn(len(s.simulationData.Users))
		newUser = s.copyAndSetUserDetails(s.simulationData.Users[userIdx])
		s.activeUserQueue = append(s.activeUserQueue, newUser)
		slog.Warn("No available users for queue rotation, allowing duplicate", "newUser", s.simulationData.Users[userIdx].UserName)
	}

	// Reassign games to ALL active users for the new period
	for _, user := range s.activeUserQueue {
		gameIdx := s.rand.Intn(len(s.simulationData.Games))
		game := &s.simulationData.Games[gameIdx]
		s.activeUserGames[user.ID] = game
	}

	s.lastQueueRotation = time.Now()

	slog.Debug("Rotated active user queue and reassigned games",
		"removedUser", removedUser.UserName,
		"addedUser", newUser.UserName,
		"queueSize", len(s.activeUserQueue),
		"gamesAssigned", len(s.activeUserGames))
}

func (s *BetSimulatorService) copyAndSetUserDetails(user domain.SimulatedUserData) *domain.SimulatedUserData {
	// 30% chance to hide stats
	copiedUser := user
	if s.rand.Float64() < 0.3 {
		copiedUser.HideAllStats = true
	} else {
		copiedUser.HideAllStats = false
	}

	// 30% chance to hide user name
	if s.rand.Float64() < 0.3 {
		copiedUser.GhostMode = true
		copiedUser.UserName = "Hidden"
		if s.rand.Float64() < 0.9 {
			copiedUser.HideAllStats = true
		}
	}
	return &copiedUser
}
